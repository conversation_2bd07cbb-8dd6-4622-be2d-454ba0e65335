{"name": "erp", "version": "0.1.0", "private": true, "dependencies": {"@mapbox/search-js-react": "^1.0.0-beta.18", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/mapbox-gl": "^3.1.0", "antd": "^5.15.0", "dayjs": "^1.11.10", "firebase": "^10.8.1", "gantt-task-react": "^0.3.9", "localforage": "^1.10.0", "mapbox-gl": "^3.2.0", "match-sorter": "^6.3.4", "mathjs": "^12.4.0", "react": "^18.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-map-gl": "^7.1.7", "react-router-dom": "^6.22.2", "react-scripts": "5.0.1", "sort-by": "^1.2.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
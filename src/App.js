import { useContext } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { globalContext } from "./global-context";
import Signin from "./pages/signin";
import Layout from "./components/layout";
import Timeclock from "./pages/timeclock";
import AdminLayout from "./components/admin-layout";
import Home from "./pages/home";
import AdminControl from "./admin-control";
import AddCustomer from "./components/add-customer";
import AddSale from "./components/add-sale";
import LayoutV2 from "./v2/components/layout-v2";
import StandartHome from "./v2/pages/standart-home";
import AdminLayoutV2 from "./v2/components/admin-layout-v2";
import AdminHome from "./v2/pages/admin-home";
import AdminCustomersV2 from "./v2/pages/admin-customers";
import AdminShifts from "./v2/pages/admin-shifts";
import AdminSales from "./v2/pages/admin-sales";
import AdminUsers from "./v2/pages/admin-users";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        <Route path="*" element={<Navigate to="" />} />
        {loged ? (
          <Route>
            <Route path="">
              <Route element={<LayoutV2 />}>
                <Route path="" element={<StandartHome />} />
                <Route path="admin" element={<AdminLayoutV2 />}>
                  <Route path="" element={<AdminHome />} />
                  <Route path="customers" element={<AdminCustomersV2 />} />
                  <Route path="sales" element={<AdminSales />} />
                  <Route path="suppliers" element={"Fournisseur"} />
                  <Route path="purchases" element={"Achats"} />
                  <Route path="materials" element={"Materiels"} />
                  <Route path="planning" element={"planning"} />
                  <Route path="timeclocks" element={<AdminShifts />} />
                  <Route path="collaborators" element={<AdminUsers />} />
                </Route>
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={<Signin />} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;

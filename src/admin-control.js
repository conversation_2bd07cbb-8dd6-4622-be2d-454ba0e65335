import { doc, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import { auth, db } from "./services/firebase";

export default function AdminControl() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    onSnapshot(doc(db, "users", auth.currentUser.uid), (snapUser) =>
      setUser(snapUser.data())
    );
  }, []);

  if (user === null) {
    return (
      <>
        <div>Chargement ...</div>
      </>
    );
  }

  if (user.role !== "admin") {
    return (
      <>
        <div>Accès non autorisé</div>
      </>
    );
  }
  return <Outlet />;
}

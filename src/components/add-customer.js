import { Button, Form, Input } from "antd";
import { addDoc, collection } from "firebase/firestore";
import { db } from "../services/firebase";
import dayjs from "dayjs";

export default function AddCustomer() {
  return (
    <>
      <div style={{ display: "flex", flex: 1, flexFlow: "column" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flex: 1,
          }}
        >
          <Form
            layout="vertical"
            onFinish={(values) =>
              addDoc(collection(db, "customers"), {
                ...values,
                created: dayjs().toDate(),
                updtated: dayjs().toDate(),
              }).then(() => window.close())
            }
          >
            <Form.Item label="Nom du client" name="name">
              <Input />
            </Form.Item>
            <Form.Item label="Adresse" name="address">
              <Input />
            </Form.Item>
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Form.Item label="Code postale" name="zipCode">
                <Input />
              </Form.Item>
              <Form.Item label="Ville" name="city">
                <Input />
              </Form.Item>
            </div>
            <Button htmlType="submit" block>
              Valider
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
}

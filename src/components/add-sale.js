import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Select,
} from "antd";
import { addDoc, collection } from "firebase/firestore";
import { db } from "../services/firebase";
import dayjs from "dayjs";
import { useRef, useState } from "react";
import { AddressAutofill } from "@mapbox/search-js-react";

export default function AddSale() {
  const refSale = useRef();
  const [resume, setResume] = useState({});
  return (
    <>
      <div style={{ display: "flex", flex: 1, flexFlow: "column" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flex: 1,
          }}
        >
          <Form
            onFieldsChange={(e) => {
              setResume({
                ...resume,
                [e[0].name[0]]: e[0].value,
              });
            }}
            ref={refSale}
            layout="vertical"
            onFinish={(values) =>
              addDoc(collection(db, "customers"), {
                ...values,
                created: dayjs().toDate(),
                updtated: dayjs().toDate(),
              }).then(() => window.close())
            }
          >
            <Form.Item label="Nom du chantier" name="name">
              <Input />
            </Form.Item>
            <Form.Item label="Client" name="customer">
              <Select options={[]} />
            </Form.Item>
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Form.Item label="Dates" name="dates">
                <DatePicker.RangePicker />
              </Form.Item>
            </div>
            <Form.Item label="Adresse" name="address">
              <AddressAutofill accessToken="pk.eyJ1IjoidHNpbm92ZWMiLCJhIjoiY2w3cTM5ZjNoMDFpYTN3bmswcTFhMjR0aSJ9.o3hr8eN-OLZ1lxj83mzNNw">
                <Input
                  name="address"
                  placeholder="Address"
                  type="text"
                  autoComplete="address-line1"
                />
              </AddressAutofill>
            </Form.Item>
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Form.Item label="Code postale" name="zipCode">
                <Input autoComplete="postal-code" />
              </Form.Item>
              <Form.Item label="Ville" name="city">
                <Input />
              </Form.Item>
            </div>
            <Divider />
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Form.Item label="Prix de revient" name="cost">
                <InputNumber suffix="€" />
              </Form.Item>
              <Form.Item label="Prix de vente" name="sell">
                <InputNumber suffix="€" />
              </Form.Item>
            </div>
            <Alert
              style={{ padding: "6px 12px" }}
              message="Synthese budgetaire"
              description={
                <>
                  <div>
                    Marge théotique:{" "}
                    {(
                      ((resume.sell - resume.cost) / resume.sell) *
                      100
                    ).toPrecision(3)}
                    %
                  </div>
                </>
              }
            />
            <Divider plain orientation="left">
              Provisions des coûts
            </Divider>
            <Form.Item label="Main d'oeuvre" name="labor">
              <InputNumber suffix="€" style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item label="Outillage & Materiel" name="toolsAndMaterials">
              <InputNumber suffix="€" style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item label="Materiaux" name="materials">
              <InputNumber suffix="€" style={{ width: "100%" }} />
            </Form.Item>
            <Button htmlType="submit" block>
              Valider
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
}

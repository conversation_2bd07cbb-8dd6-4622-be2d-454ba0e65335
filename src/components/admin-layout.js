import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Space,
  Switch,
  Table,
  Tabs,
  Tag,
  Typography,
  message,
} from "antd";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  onSnapshot,
  updateDoc,
} from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../services/firebase";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import { sum } from "mathjs";
import { CSVLink } from "react-csv";
import { RiErrorWarningFill } from "react-icons/ri";
import sortBy from "sort-by";

dayjs.locale("fr");

export default function AdminLayout() {
  const [timeclock, setTimeClock] = useState([]);
  const [sales, setSales] = useState([]);
  const [dates, setDates] = useState([dayjs().startOf("week"), dayjs()]);
  const [customers, setCustomers] = useState([]);
  const [costCenters, setCostCenter] = useState([]);
  const [users, setUsers] = useState([]);
  const [shift, setShift] = useState(null);

  useEffect(() => {
    onSnapshot(collection(db, "users"), (snapOnUsers) =>
      setUsers(snapOnUsers.docs.map((c) => ({ id: c.id, data: c.data() })))
    );

    onSnapshot(collection(db, "costCenter"), (snapOnCostCenter) =>
      setCostCenter(
        snapOnCostCenter.docs.map((c) => ({ id: c.id, data: c.data() }))
      )
    );

    onSnapshot(collection(db, "customers"), (snapOnCustomers) =>
      setCustomers(
        snapOnCustomers.docs.map((c) => ({ id: c.id, data: c.data() }))
      )
    );
    onSnapshot(collection(db, "timeclock"), (snapOnTimeClock) =>
      setTimeClock(
        snapOnTimeClock.docs.map((t) => ({ id: t.id, data: t.data() }))
      )
    );
    onSnapshot(collection(db, "sales"), (snapOnSales) =>
      setSales(snapOnSales.docs.map((s) => ({ id: s.id, data: s.data() })))
    );
  }, []);
  return (
    <>
      <Modal
        open={shift}
        onCancel={() => setShift(null)}
        destroyOnClose
        title={
          shift &&
          `Pointage de ${shift.data.user.firstName} ${shift.data.user.lastName} `
        }
        width={640}
        footer={null}
      >
        {shift && (
          <>
            <div
              style={{
                display: "flex",
                gap: 12,
                borderBottom: "1px solid #00000020",
                marginBottom: 12,
              }}
            >
              <div style={{ minWidth: 200 }}>DATE</div>
              <div style={{ minWidth: 60 }}>DEBUT</div>
              <div style={{ minWidth: 60 }}>FIN</div>
              <div style={{ minWidth: 80 }}>PRESENCE</div>
              <div>DUREE</div>
            </div>
            <div style={{ display: "flex", gap: 12 }}>
              <div style={{ minWidth: 200 }}>
                {`${dayjs(shift.data.date.seconds * 1000).format(
                  "dddd DD MMMM YYYY"
                )}`}
              </div>
              <div style={{ minWidth: 60 }}>
                {`${dayjs(shift.data.start.seconds * 1000).format("HH:mm")}`}
              </div>
              <div style={{ minWidth: 60 }}>
                {`${dayjs(shift.data.end.seconds * 1000).format("HH:mm")}`}
              </div>
              <div style={{ minWidth: 80 }}>
                {`${
                  dayjs(shift.data.end.seconds * 1000).diff(
                    dayjs(shift.data.start.seconds * 1000),
                    "minute"
                  ) / 60
                }`}
              </div>
              <div>
                {`${
                  (dayjs(shift.data.end.seconds * 1000).diff(
                    dayjs(shift.data.start.seconds * 1000),
                    "minute"
                  ) -
                    45) /
                  60
                }`}
              </div>
            </div>
            <div
              style={{
                marginTop: 12,
                paddingTop: 12,
                backgroundColor: "#00000005",
                padding: 6,
                border: "1px solid #00000020",
              }}
            >
              COMMENTAIRE: {shift.data.comment}
            </div>
            <div style={{ marginTop: 12 }}>
              <Select
                onChange={(value) =>
                  updateDoc(doc(db, "timeclock", shift.id), {
                    customer: {
                      id: value,
                      data: null,
                    },
                  }).then(() =>
                    setShift({
                      ...shift,
                      data: {
                        ...shift.data,
                        customer: {
                          id: value,
                          data: null,
                        },
                      },
                    })
                  )
                }
                variant="filled"
                style={{ width: "100%" }}
                value={shift.data.customer.id}
                options={customers.map((c) => ({
                  value: c.id,
                  label: c.data.name,
                }))}
              />
            </div>
            <div style={{ marginTop: 12 }}>
              <Input.TextArea
                onChange={(e) =>
                  updateDoc(doc(db, "timeclock", shift.id), {
                    remark: e.target.value,
                    updated: dayjs().toDate(),
                  })
                }
                value={shift.data.remark}
                placeholder="Remarque"
              />
            </div>
            <div style={{ marginTop: 24, textAlign: "right" }}>
              <Button.Group>
                <Button
                  onClick={() =>
                    deleteDoc(doc(db, "timeclock", shift.id)).then(() =>
                      setShift(null)
                    )
                  }
                >
                  Supprimer
                </Button>
                <Button
                  onClick={() =>
                    updateDoc(doc(db, "timeclock", shift.id), {
                      state: "Refusé",
                    })
                  }
                >
                  Refuser
                </Button>
                <Button
                  onClick={() =>
                    updateDoc(doc(db, "timeclock", shift.id), {
                      state: "Validé",
                    }).then(() => message.success("Etat modifié."))
                  }
                >
                  Valider
                </Button>
              </Button.Group>
            </div>
          </>
        )}
      </Modal>
      <div style={{ padding: "0px 2%" }}>
        <Tabs>
          <Tabs.TabPane key={1} tab="Vue d'ensemble"></Tabs.TabPane>
          <Tabs.TabPane key={2} tab="Pointages">
            <div style={{ padding: "12px 0px" }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <div>
                  <Typography.Title level={4}>Pointages</Typography.Title>
                </div>
                <div>
                  <Space>
                    <DatePicker.RangePicker
                      value={dates}
                      onChange={(e) => setDates(e)}
                    />
                    <CSVLink
                      data={timeclock.map((t) => ({
                        "NOM DE FAMILLES": t.data.user.lastName,
                        "DATE DU POINTAGE": dayjs(
                          t.data.date.seconds * 1000
                        ).format("DD/MM/YYYY"),
                        PRENOM: t.data.user.firstName,
                        "DATE DE DEBUT": dayjs(
                          t.data.start.seconds * 1000
                        ).format("dddd DD MMMM YYYY HH:mm"),
                        "DATE DE FIN": dayjs(t.data.end.seconds * 1000).format(
                          "dddd DD MMMM YYYY HH:mm"
                        ),
                        "TOTAL HEURE": (
                          dayjs(t.data.end.seconds * 1000)
                            .diff(t.data.start.seconds * 1000, "minute")
                            .toFixed() / 60
                        )
                          .toString()
                          .replace(".", ","),
                        ETAT: t.data.state,
                        COUT:
                          (t.data.user.cost *
                            dayjs(t.data.end.seconds * 1000)
                              .diff(t.data.start.seconds * 1000, "minute")
                              .toFixed()) /
                          60,
                        ACTIVITE: t.data.activity,
                      }))}
                    >
                      <Button>Exporter</Button>
                    </CSVLink>
                  </Space>
                </div>
              </div>
            </div>
            <div>
              <Table
                bordered
                size="small"
                dataSource={timeclock
                  .filter(
                    (f) =>
                      (dayjs(f.data.date.seconds * 1000) >= dates[0]) &
                      (dayjs(f.data.date.seconds * 1000) <= dates[1]) &
                      !f.data.version
                  )
                  .sort(sortBy("data.date"))}
                columns={[
                  {
                    title: "Collaborateur",
                    render: (e) => (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "start",
                          gap: 10,
                        }}
                      >
                        {timeclock.filter(
                          (f) =>
                            (dayjs(f.data.date.seconds * 1000).format(
                              "DD/MM/YYYY"
                            ) ===
                              dayjs(e.data.date.seconds * 1000).format(
                                "DD/MM/YYYY"
                              )) &
                            (e.data.userUid === f.data.userUid)
                        ).length >= 2 && <RiErrorWarningFill color="red" />}
                        <div
                          title={
                            timeclock.filter(
                              (f) =>
                                (dayjs(f.data.date.seconds * 1000).format(
                                  "DD/MM/YYYY"
                                ) ===
                                  dayjs(e.data.date.seconds * 1000).format(
                                    "DD/MM/YYYY"
                                  )) &
                                (e.data.userUid === f.data.userUid)
                            ).length >= 2 && "Anomalie risque doublon"
                          }
                          style={{
                            color:
                              timeclock.filter(
                                (f) =>
                                  (dayjs(f.data.date.seconds * 1000).format(
                                    "DD/MM/YYYY"
                                  ) ===
                                    dayjs(e.data.date.seconds * 1000).format(
                                      "DD/MM/YYYY"
                                    )) &
                                  (e.data.userUid === f.data.userUid)
                              ).length >= 2
                                ? "red"
                                : "black",
                          }}
                        >{`${e.data.user.firstName} ${e.data.user.lastName}`}</div>
                      </div>
                    ),
                    filters: users.sort(sortBy("data.firstName")).map((u) => ({
                      text: `${u.data.firstName} ${u.data.lastName}`,
                      value: u.id,
                    })),
                    onFilter: (value, records) =>
                      [records.data.userUid].includes(value),
                    width: 260,
                  },
                  {
                    title: "Date",
                    render: (e) =>
                      `${dayjs(e.data.date.seconds * 1000).format(
                        "dddDD MMMM YY"
                      )}`,
                    width: 200,
                  },
                  {
                    title: "Horaires",
                    render: (e) =>
                      `${dayjs(e.data.start.seconds * 1000).format(
                        "HH:mm"
                      )} → ${dayjs(e.data.end.seconds * 1000).format(
                        "HH:mm"
                      )} = ${(
                        dayjs(e.data.end.seconds * 1000).diff(
                          dayjs(e.data.start.seconds * 1000),
                          "minute"
                        ) / 60
                      ).toPrecision(3)}`,
                  },
                  {
                    title: "Etat",
                    render: (e) => <Tag>{e.data.state}</Tag>,
                  },
                  {
                    render: (e) => (
                      <Button onClick={() => setShift(e)}>Ouvrir</Button>
                    ),
                    align: "right",
                  },
                ]}
              />
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane key={3} tab="Affaires">
            <div>
              <div
                style={{
                  padding: "12px 0px",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <div>
                  <Typography.Title level={4}>Affaires</Typography.Title>
                </div>
                <div>
                  <Button
                    onClick={() =>
                      Modal.info({
                        title: "Nouvelle affaire",
                        footer: null,
                        closable: true,
                        content: (
                          <>
                            <Form
                              layout="vertical"
                              onFinish={(v) =>
                                addDoc(collection(db, "sales"), {
                                  ...v,
                                  created: dayjs().toDate(),
                                  updated: dayjs().toDate(),
                                }).then(Modal.destroyAll())
                              }
                            >
                              <Form.Item label="Titre" name="name">
                                <Input />
                              </Form.Item>
                              <Form.Item label="Client" name="customer">
                                <Select
                                  options={customers.map((c) => ({
                                    value: c.id,
                                    label: c.data.name,
                                  }))}
                                />
                              </Form.Item>
                              <Form.Item label="Société" name="costCenter">
                                <Select
                                  options={costCenters.map((cc) => ({
                                    value: cc.id,
                                    label: cc.data.name,
                                  }))}
                                />
                              </Form.Item>
                              <Form.Item label="Code devis" name="code">
                                <Input />
                              </Form.Item>
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 12,
                                }}
                              >
                                <Form.Item
                                  style={{ flex: 1 }}
                                  label="Prix de revient"
                                  name="cost"
                                >
                                  <InputNumber
                                    style={{ width: "100%" }}
                                    suffix="€"
                                  />
                                </Form.Item>
                                <Form.Item
                                  style={{ flex: 1 }}
                                  label="Prix de vente"
                                  name="sell"
                                >
                                  <InputNumber
                                    style={{ width: "100%" }}
                                    suffix="€"
                                  />
                                </Form.Item>
                              </div>
                              <Form.Item label="Temps estimé" name="hoursT">
                                <InputNumber
                                  style={{ width: "100%" }}
                                  suffix="heure(s)"
                                />
                              </Form.Item>
                              <Button htmlType="submit" type="primary" block>
                                Valider
                              </Button>
                            </Form>
                          </>
                        ),
                      })
                    }
                  >
                    Ajouter
                  </Button>
                </div>
              </div>
            </div>

            <Table
              bordered
              size="small"
              dataSource={sales}
              columns={[
                { title: "Titre", render: (e) => e.data.name },
                { title: "Code devis", render: (e) => e.data.code },
                { title: "Prix de revient", render: (e) => e.data.cost },
                { title: "Prix de vente", render: (e) => e.data.sell },
                {
                  title: "Marge",
                  render: (e) =>
                    ((e.data.sell - e.data.cost) / e.data.sell).toPrecision(3) *
                      100 +
                    "%",
                },
                {
                  title: "Pointages",
                  render: (e) =>
                    timeclock.filter((t) => t.data.sales === e.id).length,
                },
                {
                  title: "Temps pointé (h)",
                  render: (e) =>
                    (
                      sum(
                        timeclock
                          .filter((f) => f.data.sales === e.id)
                          .map((ch) => ch.data.duration)
                      ) / 60
                    ).toPrecision(3),
                },
                {
                  title: "Temps estimé (h)",
                  render: (e) => e.data.hoursT,
                },
                {
                  title: "Cout horaire",
                  render: (e) =>
                    sum(
                      timeclock
                        .filter((f) => f.data.sales === e.id)
                        .map((ch) => ch.data.cost)
                    ),
                },
                {
                  render: (e) => (
                    <Popconfirm
                      description="Supprimer ?"
                      onConfirm={() => deleteDoc(doc(db, "sales", e.id))}
                    >
                      <Button>Supprimer</Button>
                    </Popconfirm>
                  ),
                  width: 1,
                },
              ]}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Clients" key={4}>
            <div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "12px 0px",
                }}
              >
                <div>
                  <Typography.Title level={4}>Clients</Typography.Title>
                </div>
                <div>
                  <Button onClick={() => window.open("/new-customer")}>
                    Ajouter
                  </Button>
                </div>
              </div>
            </div>
            <Table
              size="small"
              bordered
              dataSource={customers}
              columns={[
                {
                  title: "Nom",
                  render: (e) => (
                    <Input
                      onChange={(v) =>
                        updateDoc(doc(db, "customers", e.id), {
                          name: v.target.value,
                        })
                      }
                      value={e.data.name}
                      variant="filled"
                    />
                  ),
                },
                {
                  title: "Adresse",
                  render: (e) => (
                    <Input
                      onChange={(v) =>
                        updateDoc(doc(db, "customers", e.id), {
                          address: v.target.value,
                        })
                      }
                      value={e.data.address}
                      variant="filled"
                    />
                  ),
                },
                {
                  title: "Ville",
                  render: (e) => (
                    <Input
                      onChange={(v) =>
                        updateDoc(doc(db, "customers", e.id), {
                          city: v.target.value,
                        })
                      }
                      value={e.data.city}
                      variant="filled"
                    />
                  ),
                },
                {
                  title: "Code postale",
                  render: (e) => (
                    <Input
                      onChange={(v) =>
                        updateDoc(doc(db, "customers", e.id), {
                          zipCode: v.target.value,
                        })
                      }
                      value={e.data.zipCode}
                      variant="filled"
                    />
                  ),
                },
                {
                  title: "Affaires/Chantiers",
                  render: (e) => (
                    <Select
                      style={{ width: "100%" }}
                      mode="multiple"
                      options={sales.map((m) => ({
                        value: m.id,
                        label: m.data.name,
                      }))}
                      value={sales
                        .filter((f) => f.data.customer === e.id)
                        .map((m) => ({
                          value: m.id,
                          label: m.data.code + " - " + m.data.name,
                        }))}
                    />
                  ),
                },
                {
                  render: (e) => (
                    <Button
                      size="small"
                      onClick={() => deleteDoc(doc(db, "customers", e.id))}
                    >
                      Supprimer
                    </Button>
                  ),
                  width: 1,
                },
              ]}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Societes" key={5}>
            <Table
              dataSource={costCenters}
              columns={[
                {
                  title: "Nom",
                  render: (e) => e.data.name,
                },
                {
                  title: "Siren",
                  render: (e) => e.data.siren,
                },
              ]}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Collaborateurs" key={6}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "12px 0px",
              }}
            >
              <div>
                <Typography.Title level={4}>Collaborateurs</Typography.Title>
              </div>
              <div></div>
            </div>
            <div>
              <Table
                size="small"
                dataSource={users.sort(sortBy("data.firstName"))}
                columns={[
                  {
                    title: "Prénom",
                    render: (e) => (
                      <Input
                        variant="filled"
                        value={e.data.firstName}
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            firstName: v.target.value,
                            updated: dayjs().toDate(),
                          })
                        }
                      />
                    ),
                  },
                  {
                    title: "Nom de famille",
                    render: (e) => (
                      <Input
                        variant="filled"
                        value={e.data.lastName}
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            lastName: v.target.value,
                            updated: dayjs().toDate(),
                          })
                        }
                      />
                    ),
                  },
                  {
                    title: "email",
                    render: (e) => (
                      <Input
                        disabled
                        variant="filled"
                        value={e.data.email}
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            email: v.target.value,
                            updated: dayjs().toDate(),
                          })
                        }
                      />
                    ),
                  },
                  {
                    title: "Admin",
                    render: (e) => (
                      <Switch
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            role: v.valueOf() ? "admin" : null,
                            updated: dayjs().toDate(),
                          })
                        }
                        value={e.data.role === "admin" ? true : false}
                      />
                    ),
                  },
                  {
                    title: "Job",
                    render: (e) => (
                      <Input
                        variant="filled"
                        value={e.data.job}
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            job: v.target.value,
                            updated: dayjs().toDate(),
                          })
                        }
                      />
                    ),
                  },
                  {
                    title: "Coût",
                    render: (e) => (
                      <InputNumber
                        suffix="€ / h"
                        variant="filled"
                        value={e.data.cost}
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            cost: v,
                            updated: dayjs().toDate(),
                          })
                        }
                      />
                    ),
                  },
                  {
                    title: "Société",
                    render: (e) => (
                      <Select
                        onChange={(v) =>
                          updateDoc(doc(db, "users", e.id), {
                            costCenter: v,
                            updated: dayjs().toDate(),
                          })
                        }
                        value={e.data.costCenter}
                        options={costCenters.map((c) => ({
                          value: c.id,
                          label: c.data.name,
                        }))}
                      />
                    ),
                    filters: costCenters.map((c) => ({
                      value: c.id,
                      text: c.data.name,
                    })),
                    onFilter: (value, records) =>
                      [records.data.costCenter].includes(value),
                  },
                  {
                    render: (e) => (
                      <Popconfirm
                        title="Supprimer"
                        onConfirm={() => deleteDoc(doc(db, "users", e.id))}
                      >
                        <Button>Supprimer</Button>
                      </Popconfirm>
                    ),
                    align: "right",
                  },
                ]}
              />
            </div>
          </Tabs.TabPane>
        </Tabs>
      </div>
    </>
  );
}

import { <PERSON><PERSON>, <PERSON>, Di<PERSON>r, Modal, Popover } from "antd";
import {
  collection,
  doc,
  getDoc,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { auth, db } from "../services/firebase";
import dayjs from "dayjs";

export default function Layout() {
  const navigate = useNavigate();
  const [user, setUser] = useState({});
  const [customers, setCustomers] = useState([]);
  const [update, setUpdate] = useState([]);

  useEffect(() => {
    onSnapshot(
      query(collection(db, "updates"), where("publish", "==", true)),
      (snapOnUpdates) =>
        setUpdate(snapOnUpdates.docs.map((u) => ({ id: u.id, data: u.data() })))
    );
    getDoc(doc(db, "users", auth.currentUser.uid)).then((snapUser) =>
      setUser(snapUser.data())
    );
    onSnapshot(collection(db, "customers"), (snapOnCustomers) =>
      setCustomers(
        snapOnCustomers.docs.map((d) => ({ id: d.id, data: d.data() }))
      )
    );
  }, []);

  return (
    <>
      <div
        style={{
          display: "flex",
          minHeight: "100svh",
          flexFlow: "column",
        }}
      >
        {update.length > 0 && (
          <div
            onClick={() =>
              Modal.info({
                title: "Mise à jour",
                closable: true,
                width: 600,
                content: (
                  <>
                    <div>
                      {update.map((u) => (
                        <Card
                          size="small"
                          key={u.id}
                          title={u.data.title}
                          extra={dayjs(u.data.date.seconds * 1000).format(
                            "ddd DD MMMM HH:mm"
                          )}
                        >
                          <div>
                            Etat de l'application pendant la mise à jour:
                            {u.data.appRun ? "Fonctionnel" : "inutilisable"}
                          </div>
                          <div>Durée estimée: {u.data.duration}min(s)</div>
                        </Card>
                      ))}
                    </div>
                  </>
                ),
              })
            }
            style={{
              padding: 12,
              textAlign: "center",
              backgroundColor: "orange",
              cursor: "pointer",
              fontSize: 12,
              color: "white",
            }}
          >
            {update.length} Mise(s) prévue(s)
          </div>
        )}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "12px 2%",
            borderBottom: "1px solid #00000020",
          }}
        >
          <div>ERP</div>
          <div style={{ display: "flex", gap: 6, alignItems: "center" }}>
            <Button
              onClick={() => navigate("")}
              size="small"
              type={window.location.pathname === "/" ? "primary" : "text"}
            >
              Accueil
            </Button>
            <Button
              hidden
              onClick={() => navigate("timeclock")}
              size="small"
              type={
                window.location.pathname === "/timeclock" ? "primary" : "text"
              }
            >
              Pointage
            </Button>
            <Button
              hidden
              size="small"
              type={
                window.location.pathname === "/absences" ? "primary" : "text"
              }
            >
              Absences
            </Button>
            <Button
              hidden
              size="small"
              type={window.location.pathname === "/folder" ? "primary" : "text"}
            >
              Documents
            </Button>
            <Button
              onClick={() => navigate("admin")}
              hidden={user.role === "admin" ? false : true}
              size="small"
              type={
                window.location.pathname.match("admin") ? "primary" : "text"
              }
            >
              Admin
            </Button>
            <Divider type="vertical" />
            <Popover
              title="Profil"
              placement="bottomRight"
              trigger="click"
              content={
                <>
                  <div style={{ width: 220 }}>
                    <Button
                      danger
                      type="primary"
                      onClick={() => auth.signOut()}
                      size="small"
                      block
                      style={{ textAlign: "left" }}
                    >
                      Déconnexion
                    </Button>
                  </div>
                </>
              }
            >
              <Button size="small" shape="round">
                {user.firstName}
              </Button>
            </Popover>
          </div>
        </div>
        <div style={{ flex: 1, display: "flex", flexFlow: "column" }}>
          <Outlet context={{ customers, user }} />
        </div>
      </div>
    </>
  );
}

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { BrowserRouter } from "react-router-dom";
import GlobalContext from "./global-context";
import fr_FR from "antd/locale/fr_FR";
import "dayjs/locale/fr";
import "./styles.css";
import "antd/dist/reset.css";
import { ConfigProvider } from "antd";
import "mapbox-gl/dist/mapbox-gl.css";

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <React.StrictMode>
    <ConfigProvider
      locale={fr_FR}
      theme={{
        token: {
          borderRadius: 3,
          colorPrimary: "orangered",
          marginLG: 8,
        },
      }}
    >
      <GlobalContext>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </GlobalContext>
    </ConfigProvider>
  </React.StrictMode>
);

import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Form,
  Input,
  List,
  Modal,
  Popover,
  Select,
  Space,
  Statistic,
  Tag,
  TimePicker,
  message,
} from "antd";
import dayjs, { Dayjs } from "dayjs";
import { useOutletContext } from "react-router-dom";
import { functions } from "../services/functions";
import { useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  where,
} from "firebase/firestore";
import { auth, db } from "../services/firebase";
import { RiMore2Fill } from "react-icons/ri";
import sortBy from "sort-by";

export default function Home() {
  const { user } = useOutletContext();
  const [dates, setDates] = useState(dayjs().startOf("month"));
  const [timeclocks, setTimeClocks] = useState([]);
  const [refresh, setRefres] = useState(dayjs());
  const [width, setWidth] = useState(document.body.offsetWidth);
  const [newShift, setNewShift] = useState(false);
  const [customer, setCustomer] = useState([]);

  window.addEventListener("resize", () => setWidth(document.body.offsetWidth));

  useEffect(() => {
    getDocs(collection(db, "customers")).then((snapCustomers) =>
      setCustomer(snapCustomers.docs.map((c) => ({ id: c.id, data: c.data() })))
    );
    getDocs(
      query(
        collection(db, "timeclock"),
        where("userUid", "==", auth.currentUser.uid),
        where("date", "<=", dates.endOf("month").toDate()),
        where("date", ">=", dates.toDate())
      )
    ).then((snapTimeclock) => {
      setTimeClocks(
        snapTimeclock.docs.map((t) => ({ id: t.id, data: t.data() }))
      );
    });
  }, [dates, refresh]);
  return (
    <>
      <Modal
        destroyOnClose
        open={newShift}
        onCancel={() => setNewShift(false)}
        title="Pointage"
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={(values) => {
            console.log(values);
            addDoc(collection(db, "timeclock"), {
              date: values.date.toDate(),
              start: values.hours[0].toDate(),
              end: values.hours[1].toDate(),
              user: user,
              customer: {
                id: values.customer || "Vide",
              },
              state: "En attente",
              created: dayjs().toDate(),
              updated: dayjs().toDate(),
              comment: values.comment,
              activity: values.activity || "Vide",
              userUid: auth.currentUser.uid,
            })
              .then(() => {
                message.success("Pointage envoyé");
                setDates(dayjs().startOf("month"));
              })
              .catch((error) => message.error(error));
          }}
        >
          <Form.Item name="date" label="Date">
            <DatePicker
              disabledDate={(e) => e > dayjs()}
              size="large"
              format={"dddd DD MMMM YYYY"}
              style={{ width: "100%" }}
              variant="filled"
            />
          </Form.Item>
          <Form.Item name={"activity"} label="Activité">
            <Select
              size="large"
              variant="filled"
              options={[
                {
                  value: "Chantier",
                },
                {
                  value: "Formation",
                },
                {
                  value: "Absence",
                },
                {
                  value: "Sous activité",
                },
              ]}
            />
          </Form.Item>
          <Form.Item name="hours" label="Horaires">
            <TimePicker.RangePicker
              format={"HH:mm"}
              size="large"
              variant="filled"
              style={{
                width: "100%",
              }}
            />
          </Form.Item>
          <Form.Item label="Client" name="customer">
            <Select
              allowClear
              style={{ width: "100%" }}
              size="large"
              variant="filled"
              options={customer.map((c) => ({
                value: c.id,
                label: c.data.name,
              }))}
            />
          </Form.Item>
          <Form.Item name="comment" label="Commentaire">
            <Input.TextArea variant="filled" size="large" />
          </Form.Item>
          <div>
            <Button block htmlType="submit" type="primary">
              Valider
            </Button>
          </div>
        </Form>
      </Modal>
      <div
        style={{
          display: "flex",
          flexFlow: "column",
        }}
      >
        <div
          style={{
            padding: "24px 2%",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ fontWeight: 600, fontSize: 20 }}>{`${
            dayjs().hour() < 18 ? "Bonjour" : "Bonsoir"
          } ${user.firstName}`}</div>
          <div>
            <Space>
              <DatePicker
                disabledDate={(e) => e > dayjs()}
                onChange={(e) => setDates(e)}
                value={dates}
                picker="month"
                format={"MMMM YYYY"}
                allowClear={false}
              />
              <Button onClick={() => setNewShift(true)}>Pointer</Button>
            </Space>
          </div>
        </div>
      </div>
      <div style={{ padding: "12px 2%" }}>
        <Space size={24}>
          <Card
            size="small"
            style={{
              minWidth: 160,
            }}
          >
            <Statistic
              title="THEORIQUE"
              value={
                functions.utilities.dates
                  .list(
                    dates.startOf("month").toDate(),
                    dates.endOf("month").toDate()
                  )
                  .filter((f) => ![0, 6].includes(dayjs(f).day())).length
              }
            />
          </Card>
          <Card
            size="small"
            style={{
              minWidth: 160,
            }}
          >
            <Statistic title="POINTÉS" value={timeclocks.length} />
          </Card>
        </Space>
      </div>
      <div style={{ padding: "12px 2%" }}>
        <List
          grid={{
            gutter: [0, 12],
            column: 1,
          }}
          split
          dataSource={timeclocks.sort(sortBy("-data.date"))}
          rowKey={(e) => e.id}
          renderItem={(e, index) => (
            <div
              style={{
                padding: 12,
                backgroundColor: "#00000005",
                border: "1px solid #00000020",
                cursor: "pointer",
                borderRadius: 3,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 24,
                overflow: "hidden",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 24,
                  flex: 1,
                  borderRight: "1px solid #00000020",
                }}
              >
                <div>{index + 1}</div>
                <div style={{ flex: 0.08 }}>
                  <Tag
                    color={
                      e.data.state === "En attente"
                        ? "blue"
                        : e.data.state === "Validé"
                        ? "green"
                        : "red"
                    }
                  >
                    {e.data.state}
                  </Tag>
                </div>
                <div style={{ flex: 0.16, minWidth: 160 }}>
                  {dayjs(e.data.date.seconds * 1000).format(
                    "dddd DD MMMM YYYY"
                  )}
                </div>
                <div
                  style={{ flex: 0.1, minWidth: 100 }}
                  hidden={width < 600}
                >{`${dayjs(e.data.start.seconds * 1000).format(
                  "HH:mm"
                )} → ${dayjs(e.data.end.seconds * 1000).format("HH:mm")}`}</div>
                <div hidden={width < 800}>
                  {(
                    dayjs(e.data.end.seconds * 1000).diff(
                      dayjs(e.data.start.seconds * 1000),
                      "minute"
                    ) / 60
                  ).toPrecision(3)}
                </div>
                <div>
                  <Select
                    style={{ width: 200 }}
                    variant="borderless"
                    value={e.data.customer.id}
                    options={customer.map((cus) => ({
                      value: cus.id,
                      label: cus.data.name,
                    }))}
                  />
                </div>
              </div>
              <div style={{ flex: 0 }}>
                <Popover
                  trigger="click"
                  placement="bottomRight"
                  arrow={false}
                  overlayInnerStyle={{ padding: 0 }}
                  content={
                    <>
                      <div style={{ width: 220 }}>
                        <div
                          style={{
                            padding: "6px 12px",
                            borderBottom: "1px solid #00000020",
                            backgroundColor: "#00000005",
                          }}
                        >{`${dayjs(e.data.date.seconds * 1000).format(
                          "dddd DD MMMM YYYY"
                        )}`}</div>
                        <div style={{ padding: "6px 6px" }}>
                          <div
                            className="btnPopOverUser"
                            onClick={() =>
                              addDoc(collection(db, "timeclock"), {
                                ...e.data,
                                created: dayjs().toDate(),
                                updated: dayjs().toDate(),
                                state: "En attente",
                                date: dayjs(e.data.date.seconds * 1000)
                                  .add(1, "day")
                                  .toDate(),
                              }).then(() => setRefres(dayjs()))
                            }
                          >
                            Dupliquer (+1j)
                          </div>
                          <div
                            className="btnPopOverUser"
                            onClick={() =>
                              deleteDoc(doc(db, "timeclock", e.id)).then(() =>
                                setRefres(dayjs())
                              )
                            }
                          >
                            Supprimer
                          </div>
                        </div>
                      </div>
                    </>
                  }
                >
                  <Button
                    disabled={!["En attente", "Refusé"].includes(e.data.state)}
                    size="small"
                    type="text"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    icon={<RiMore2Fill />}
                  />
                </Popover>
              </div>
            </div>
          )}
        />
      </div>
    </>
  );
}

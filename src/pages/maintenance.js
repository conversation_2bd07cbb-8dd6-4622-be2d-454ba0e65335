import { Card, Image } from "antd";
import dayjs from "dayjs";

export default function Maintenance() {
  return (
    <>
      <div
        style={{
          minHeight: "100svh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#00000007",
        }}
      >
        <Card style={{ textAlign: "center" }}>
          <div style={{ fontSize: 20, fontWeight: 600, color: "#669ba8ff" }}>
            En maintenance
          </div>
          <div style={{ color: "darkgray" }}>
            Remise en ligne dans
            {` ${(
              dayjs("2024/03/25 17:00:00").diff(dayjs(), "minute") / 60
            ).toPrecision(3)} Heure(s)`}
          </div>
          <div style={{ textAlign: "left", marginTop: 12 }}>
            <div>- Mise à jour du formulaire de pointage</div>
            <div>- Mise à jour de l'espace administrateur</div>
          </div>
          <div style={{ marginTop: 32 }}>
            <Image
              preview={false}
              width={200}
              src="https://ouch-cdn2.icons8.com/pOfRXyNt09146rhQ5uo7s33SoAAaU4tvaNwcrr11K0o/rs:fit:368:231/czM6Ly9pY29uczgu/b3VjaC1wcm9kLmFz/c2V0cy9wbmcvOC81/MDJiNjI4NS0wNTNl/LTQzNzAtYTI2MS1h/N2ZjMmYwNWE2NmMu/cG5n.png"
            />
          </div>
        </Card>
      </div>
    </>
  );
}

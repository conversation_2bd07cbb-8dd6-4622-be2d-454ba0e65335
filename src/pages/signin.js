import { Button, Form, Input, message } from "antd";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "../services/firebase";

export default function Signin() {
  return (
    <>
      <div
        style={{
          display: "flex",
          minHeight: "100svh",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div>
          <Form
            onFinish={(e) =>
              signInWithEmailAndPassword(auth, e.email, e.password).catch(
                (error) => message.error(error.code)
              )
            }
            layout="vertical"
            style={{ width: 400, padding: 40 }}
          >
            <Form.Item label="Adresse mail" name="email">
              <Input />
            </Form.Item>
            <Form.Item label="Mot de passe" name="password">
              <Input.Password />
            </Form.Item>
            <Button htmlType="submit" block type="primary">
              Connexion
            </Button>
          </Form>
        </div>
      </div>
    </>
  );
}

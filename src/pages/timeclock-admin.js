import { But<PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON>r, <PERSON>dal, Select, Space, Table } from "antd";
import dayjs from "dayjs";
import { collection, onSnapshot, query, where } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../services/firebase";
import { RiDeleteBinFill, RiEyeFill } from "react-icons/ri";

export default function TimeclockAdmin() {
  const [dates, setDates] = useState([dayjs().startOf("month"), dayjs()]);
  const [timeclocks, setTimeClocks] = useState([]);
  const [open, setOpen] = useState(false);
  const [timeclock, setTimeClock] = useState();

  useEffect(() => {
    onSnapshot(
      query(
        collection(db, "timeclock"),
        where("date", "<=", dayjs(dates[1]).toDate()),
        where("date", ">=", dayjs(dates[0]).toDate())
      ),
      (snapOnTimeclocks) =>
        setTimeClocks(
          snapOnTimeclocks.docs.map((t) => ({ id: t.id, data: t.data() }))
        )
    );
  }, [dates]);
  return (
    <>
      <Modal
        title={
          timeclock &&
          `${timeclock.data.user.firstName} ${timeclock.data.user.lastName}`
        }
        open={open}
        onCancel={() => {
          setTimeClock(null);
          setOpen(false);
        }}
        footer={
          <>
            <Button.Group>
              <Button>Refuser</Button>
              <Button>Valider</Button>
            </Button.Group>
          </>
        }
      >
        {timeclock && (
          <>
            <Divider plain orientation="left">
              Horaires
            </Divider>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <div>
                <div
                  style={{ fontSize: 12, fontWeight: 600, color: "darkgray" }}
                >
                  DATE
                </div>
                <div style={{ fontSize: 18 }}>
                  {dayjs(timeclock.data.date.seconds * 1000).format(
                    "dddDD MMM YYYY"
                  )}
                </div>
              </div>
              <div>
                <div
                  style={{ fontSize: 12, fontWeight: 600, color: "darkgray" }}
                >
                  DEBUT
                </div>
                <div style={{ fontSize: 18 }}>
                  {dayjs(timeclock.data.start.seconds * 1000).format("HH:mm")}
                </div>
              </div>
              <div>
                <div
                  style={{ fontSize: 12, fontWeight: 600, color: "darkgray" }}
                >
                  FIN
                </div>
                <div style={{ fontSize: 18 }}>
                  {dayjs(timeclock.data.end.seconds * 1000).format("HH:mm")}
                </div>
              </div>
              <div>
                <div
                  style={{ fontSize: 12, fontWeight: 600, color: "darkgray" }}
                >
                  DUREE
                </div>
                <div style={{ fontSize: 18 }}>
                  {(timeclock.data.duration / 60).toPrecision(3)}
                </div>
              </div>
            </div>
            <Divider plain orientation="left">
              Activité
            </Divider>
            <div>
              <div>
                <div
                  style={{ fontSize: 12, fontWeight: 600, color: "darkgray" }}
                >
                  CLIENT
                </div>
                <div>{timeclock.data.customer.data.name}</div>
              </div>
              <div style={{ paddingTop: 12 }}>
                <Select
                  size="large"
                  style={{ width: "100%" }}
                  placeholder="Affaire"
                />
              </div>
            </div>
            <Divider plain orientation="left">
              Remarques
            </Divider>
            <div
              style={{
                padding: "6px 12px",
                borderRadius: 3,
                backgroundColor: "#00000008",
              }}
            >
              {timeclock.data.comment}
            </div>
          </>
        )}
      </Modal>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px 2% 12px",
        }}
      >
        <div style={{ fontWeight: 600, fontSize: 22 }}>Pointages</div>
        <div>
          <Space>
            <DatePicker.RangePicker
              format={"dddd DD MMMM YYYY"}
              allowClear={false}
              value={dates}
              onChange={(e) => setDates(e)}
            />
          </Space>
        </div>
      </div>
      <div style={{ padding: "12px 2% 0px" }}>
        <Table
          bordered
          size="small"
          scroll={{ y: 700 }}
          dataSource={timeclocks}
          columns={[
            {
              title: "Collaborateurs",
              render: (e) => `${e.data.user.firstName} ${e.data.user.lastName}`,
            },
            {
              title: "Date",
              render: (e) =>
                `${dayjs(e.data.date.seconds * 1000).format(
                  "dddd DD MMMM YYYY"
                )}`,
            },
            {
              title: "Horaires",
              render: (e) =>
                `${dayjs(e.data.start.seconds * 1000).format(
                  "HH:mm"
                )} → ${dayjs(e.data.end.seconds * 1000).format("HH:mm")}`,
            },
            {
              render: (e) => (
                <Button.Group>
                  <Button
                    onClick={() => {
                      setTimeClock(e);
                      setOpen(true);
                    }}
                    size="small"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    icon={<RiEyeFill />}
                  />
                  <Button
                    size="small"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    icon={<RiDeleteBinFill />}
                  />
                </Button.Group>
              ),
            },
          ]}
        />
      </div>
    </>
  );
}

import {
  Button,
  DatePicker,
  Divider,
  Drawer,
  Empty,
  Form,
  Input,
  Select,
  Tag,
  TimePicker,
  message,
} from "antd";
import dayjs from "dayjs";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";
import { useEffect, useState } from "react";
import { useOutletContext } from "react-router-dom";
import { auth, db } from "../services/firebase";

export default function Timeclock() {
  const [date, setDate] = useState(dayjs());
  const [hours, setHours] = useState([dayjs().add(-8, "hour"), dayjs()]);
  const [timeclocks, setTimeClocks] = useState([]);
  const [showTime, setShowTime] = useState(false);

  useEffect(() => {
    onSnapshot(
      query(
        collection(db, "timeclock"),
        where("userUid", "==", auth.currentUser.uid)
      ),
      (snapOnTimeClocks) =>
        setTimeClocks(
          snapOnTimeClocks.docs.map((t) => ({ id: t.id, data: t.data() }))
        )
    );
  }, []);

  const { customers, user } = useOutletContext();
  return (
    <>
      <div
        style={{
          flex: 1,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Form
          layout="vertical"
          style={{ width: 400, padding: 40 }}
          onFinish={(values) => {
            addDoc(collection(db, "timeclock"), {
              user: user,
              date: values.date.toDate(),
              start: values.hours[0].toDate(),
              end: values.hours[1].toDate(),
              customer: JSON.parse(values.customer),
              state: "En attente",
              comment: values.comment,
              cost:
                dayjs(hours[1]).diff(hours[0], "minute") >= 360
                  ? ((dayjs(hours[1]).diff(hours[0], "minute") - 45) / 60) *
                    user.cost
                  : (dayjs(hours[1]).diff(hours[0], "minute") / 60) * user.cost,
              duration: dayjs(hours[1]).diff(hours[0], "minute"),
              created: dayjs().toDate(),
              updated: dayjs().toDate(),
              userUid: auth.currentUser.uid,
            }).then((event) => {
              message.success("Pointage envoyé");
              window.location.reload();
            });
          }}
        >
          <Form.Item label="Date" name="date" initialValue={date} required>
            <DatePicker
              size="large"
              allowClear={false}
              format={"dddd DD MMMM YYYY"}
              style={{ width: "100%" }}
              onChange={(e) => setDate(e)}
              disabledDate={(e) => e > dayjs()}
            />
          </Form.Item>
          <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
            <Form.Item
              label="Début"
              name="hours"
              style={{ flex: 1 }}
              help={
                (dayjs(hours[1]).diff(hours[0], "minute") - 45) / 60 > 8 &&
                `Dépassement de temps`
              }
            >
              <TimePicker.RangePicker
                ok
                size="large"
                allowClear={false}
                status={
                  (dayjs(hours[1]).diff(hours[0], "minute") - 45) / 60 > 8
                    ? "error"
                    : "null"
                }
                onChange={(e) => setHours(e)}
                style={{ width: "100%" }}
                format={"HH:mm"}
              />
            </Form.Item>
          </div>
          {dayjs(hours[1]).diff(hours[0], "minute") >= 360 && (
            <div
              style={{
                padding: 12,
                backgroundColor: "#00000005",
                border: "1px solid #00000020",
              }}
            >
              <div>Informations</div>
              <div>{`45 Minutes(s) de pause repas`}</div>
              <div>
                {`${(
                  (dayjs(hours[1]).diff(hours[0], "minute") - 45) /
                  60
                ).toPrecision(3)} Heure(s) de temps de travail`}
              </div>
            </div>
          )}
          <Form.Item label="Client" name="customer" required>
            <Select
              size="large"
              options={customers.map((c) => ({
                value: JSON.stringify(c),
                label: c.data.name,
              }))}
            />
          </Form.Item>
          <Form.Item label="Commentaire" name="comment">
            <Input.TextArea required />
          </Form.Item>
          <Button type="primary" block htmlType="submit">
            Valider
          </Button>
          <Divider />
          <Button block onClick={() => setShowTime(true)}>
            Mes Pointages
          </Button>
        </Form>
      </div>
      <Drawer
        title="Mes pointages"
        open={showTime}
        onClose={() => setShowTime(false)}
        width={600}
      >
        {timeclocks.length < 1 && <Empty description="Aucun pointage" />}
        {timeclocks.map((t) => (
          <div
            key={t.id}
            style={{
              padding: 12,
              border: "1px solid #00000020",
              borderRadius: 3,
            }}
          >
            <Tag style={{ marginBottom: 12 }}>{t.data.state}</Tag>
            <div style={{ fontSize: 16, fontWeight: 600 }}>
              {dayjs(t.data.date.seconds * 1000).format("dddd DD MMMM YYYY")}
            </div>
            <div style={{ display: "flex", gap: 12, marginBottom: 12 }}>
              <div>{`${dayjs(t.data.start.seconds * 1000).format(
                "HH:mm"
              )} → ${dayjs(t.data.end.seconds * 1000).format("HH:mm")} = ${(
                t.data.duration / 60
              ).toPrecision(3)}`}</div>
            </div>
            <div>Commentaire:</div>
            <div style={{ marginBottom: 12 }}>{t.data.comment}</div>
            <div>Message manager:</div>
            <div style={{ marginBottom: 12 }}>{t.data.remark || "Aucun"}</div>
            <div>
              <Button
                disabled={t.data.state !== "En attente" ? true : false}
                onClick={() => deleteDoc(doc(db, "timeclock", t.id))}
              >
                Supprimer
              </Button>
            </div>
          </div>
        ))}
      </Drawer>
    </>
  );
}

import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
  apiKey: "AIzaSyB-WuwhxhUrEDo-VS6Vmj2qDvMlV1_vSsI",
  authDomain: "simon-groupe.firebaseapp.com",
  databaseURL: "https://simon-groupe-default-rtdb.firebaseio.com",
  projectId: "simon-groupe",
  storageBucket: "simon-groupe.appspot.com",
  messagingSenderId: "78264811837",
  appId: "1:78264811837:web:5f11cc619f7e15172b0554",
  measurementId: "G-TZNM3HPL6D",
};

const app = initializeApp(firebaseConfig);

export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

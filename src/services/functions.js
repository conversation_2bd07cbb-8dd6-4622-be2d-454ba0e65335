import {
  addDoc,
  collection,
  deleteField,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";
import { auth, db, storage } from "./firebase";
import dayjs from "dayjs";
import { deleteObject, ref, uploadBytes } from "firebase/storage";

export const functions = {
  user: {
    signOut: () => {
      addDoc(collection(db, "logs"), {
        type: "auth",
        title: "Déconnexion",
        description: `Déconnexion de l'utilisateur ${
          auth.currentUser.email
        } le ${dayjs().format("DD MMMM YYYY")} à ${dayjs().format("HH:mm:ss")}`,
        created: dayjs().toDate(),
        userUid: auth.currentUser.uid,
        userEmail: auth.currentUser.email,
      }).then(() => auth.signOut());
    },
  },
  logs: {
    add: (type, title, description) => {
      addDoc(collection(db, "logs"), {
        type: type,
        title: title,
        description: description,
        created: dayjs().toDate(),
        userUid: auth.currentUser.uid,
        userEmail: auth.currentUser.email,
      });
    },
  },
  file: {
    add: async (file, path) => {
      const storageRef = ref(storage, path + file.name);
      await uploadBytes(storageRef, file).then(() =>
        functions.logs.add(
          "file",
          "Création",
          `L'utilisateur ${auth.currentUser.email} a ajouté un fichier ${file.name} sur ${path}.`
        )
      );
    },
    delete: async (file) => {
      await deleteObject(file);
    },
  },
  utilities: {
    dates: {
      list: (startDate, endDate) => {
        const dates = [];

        // Assurez-vous que les entrées sont des objets de type Date
        let currentDate = new Date(startDate.getTime());
        const end = new Date(endDate.getTime());

        while (currentDate <= end) {
          // Ajoute une copie de `currentDate` au tableau pour éviter des modifications par référence
          dates.push(new Date(currentDate));
          // Avance de un jour
          currentDate.setDate(currentDate.getDate() + 1);
        }

        return dates;
      },
    },
  },

  shifts: {
    listAll: async (dates) => {
      return onSnapshot(
        query(
          collection(db, "shifts"),
          where("date", "<=", dayjs(dates[1]).toDate()),
          where("date", ">=", dayjs(dates[0]).toDate())
        ),
        (snapOnShifts) =>
          snapOnShifts.docs.map((shifts) => ({
            id: shifts.id,
            data: shifts.data(),
          }))
      );
    },
  },
};

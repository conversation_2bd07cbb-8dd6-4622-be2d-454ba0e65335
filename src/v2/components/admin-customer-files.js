import { listAll, ref } from "firebase/storage";
import { useEffect, useState } from "react";
import { storage } from "../../services/firebase";
import { Button, Empty, Input, List, Spin, Upload, message } from "antd";
import { functions } from "../../services/functions";
import { RiDeleteBin2Line, RiDownload2Line, RiFile3Line } from "react-icons/ri";

export default function AdminCustomerFiles({ customer }) {
  const [files, setFiles] = useState(null);

  const getFiles = () => {
    const listRef = ref(storage, `private/${customer.id}`);
    listAll(listRef).then((files) => {
      setFiles(files.items.map((f) => f));
    });
  };

  useEffect(() => {
    getFiles();
  }, []);

  if (!files) {
    return <Spin />;
  }
  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "0px 0px 24px",
        }}
      >
        <div style={{ fontWeight: 600, fontSize: 18 }}>Documents</div>
        <div>
          <Upload
            fileList={[]}
            onChange={(e) => {
              functions.file
                .add(e.file, `private/${customer.id}/${e.file.name}`)
                .then(() => getFiles());
            }}
            multiple={false}
          >
            <Button>Ajouter</Button>
          </Upload>
        </div>
      </div>
      <div
        style={{
          display: "flex",
          flexFlow: "column",
          gap: 12,
        }}
      >
        {files.length < 1 ? (
          <Empty description="Aucun fichier" />
        ) : (
          files.map((f) => (
            <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
              <Button
                type="text"
                icon={<RiFile3Line />}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "start",
                }}
                block
              >
                {f.name}
              </Button>
              <Button.Group>
                <Button
                  icon={<RiDownload2Line />}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                />
                <Button
                  onClick={() =>
                    functions.file.delete(f).then(() => getFiles())
                  }
                  icon={<RiDeleteBin2Line />}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                />
              </Button.Group>
            </div>
          ))
        )}
      </div>
    </>
  );
}

import { Button, Empty, Space } from "antd";
import { Outlet, useNavigate, useOutletContext } from "react-router-dom";
import { functions } from "../../services/functions";
import { auth } from "../../services/firebase";

export default function AdminLayoutV2() {
  const navigate = useNavigate();
  const { user } = useOutletContext();

  if (user.role !== "admin") {
    functions.logs.add(
      "nav",
      "Navigation",
      `Tentative d'accès au centre d'administration par ${auth.currentUser.email}`
    );
    return (
      <div
        style={{
          flex: 1,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Empty
          image={
            "https://ouch-cdn2.icons8.com/4UTqLdu9xTpiCPgOE-L4a0TPqtHZniP6YR8GtEyBrtw/rs:fit:368:368/czM6Ly9pY29uczgu/b3VjaC1wcm9kLmFz/c2V0cy9wbmcvODk2/LzVkODk4MDEzLWM4/NWItNGYwNi1hNDIw/LWJiMDgxYWM4MTFi/OC5wbmc.png"
          }
          description={
            <>
              <div>
                <div
                  style={{ fontWeight: 600, fontSize: 22, color: "#ef233c" }}
                >
                  Access Denied
                </div>
                <div>
                  You do not have the rights to access the administration
                </div>
                <div>
                  <Button
                    style={{ marginTop: 12 }}
                    onClick={() => navigate("/")}
                  >
                    Retour
                  </Button>
                </div>
              </div>
            </>
          }
        />
      </div>
    );
  }

  return (
    <>
      <div style={{ padding: "0px 2%" }}>
        <div
          style={{
            padding: "12px 0px",
            borderBottom: "1px solid #00000020",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Space>
            <Button
              onClick={() => navigate("")}
              size="small"
              type={
                window.location.pathname === "/admin" ? "primary" : "text"
              }
            >
              Accueil
            </Button>
            <Button
              onClick={() => navigate("customers")}
              size="small"
              type={
                window.location.pathname === "/admin/customers"
                  ? "primary"
                  : "text"
              }
            >
              Clients
            </Button>
            <Button
              onClick={() => navigate("sales")}
              size="small"
              type={
                window.location.pathname === "/admin/sales"
                  ? "primary"
                  : "text"
              }
            >
              Affaires
            </Button>
            <Button
              onClick={() => navigate("timeclocks")}
              size="small"
              type={
                window.location.pathname === "/admin/timeclocks"
                  ? "primary"
                  : "text"
              }
            >
              Pointages
            </Button>
            <Button
              onClick={() => navigate("collaborators")}
              size="small"
              type={
                window.location.pathname === "/admin/collaborators"
                  ? "primary"
                  : "text"
              }
            >
              Collaborateurs
            </Button>
            <Button
              disabled
              onClick={() => navigate("suppliers")}
              size="small"
              type={
                window.location.pathname === "/admin/suppliers"
                  ? "primary"
                  : "text"
              }
            >
              Fournisseurs
            </Button>
            <Button
              disabled
              onClick={() => navigate("purchases")}
              size="small"
              type={
                window.location.pathname === "/admin/purchases"
                  ? "primary"
                  : "text"
              }
            >
              Achats
            </Button>
            <Button
              disabled
              onClick={() => navigate("materials")}
              size="small"
              type={
                window.location.pathname === "/admin/materials"
                  ? "primary"
                  : "text"
              }
            >
              Materiels
            </Button>
            <Button
              disabled
              onClick={() => navigate("planning")}
              size="small"
              type={
                window.location.pathname === "/admin/planning"
                  ? "primary"
                  : "text"
              }
            >
              Planning
            </Button>

          </Space>
        </div>
      </div>
      <div>
        <Outlet />
      </div>
    </>
  );
}

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Popover, Space } from "antd";
import { collection, doc, onSnapshot, query, where } from "firebase/firestore";
import { useEffect, useState } from "react";
import {
  RiLoginBoxLine,
  RiLogoutBoxLine,
  RiNotification3Fill,
  RiQuestionFill,
  RiUser3Fill,
} from "react-icons/ri";
import { auth, db } from "../../services/firebase";
import Loader from "./loader";
import { Outlet, useNavigate } from "react-router-dom";
import { functions } from "../../services/functions";

export default function LayoutV2() {
  const [user, setUser] = useState(null);
  const [notifications, setNotifications] = useState([]);

  const navigate = useNavigate();

  useEffect(() => {
    onSnapshot(doc(db, "users", auth.currentUser.uid), (snapOnUser) =>
      setUser(snapOnUser.data())
    );

    onSnapshot(
      query(
        collection(db, "notifications"),
        where("userUid", "==", auth.currentUser.uid),
        where("read", "!=", true)
      ),
      (snapOnNotifications) =>
        setNotifications(
          snapOnNotifications.docs.map((n) => ({ id: n.id, data: n.data() }))
        )
    );
  }, []);

  if (!user) {
    return <Loader />;
  }
  return (
    <>
      <div style={{ display: "flex", minHeight: "100svh", flexFlow: "column" }}>
        <div
          style={{
            padding: "12px 2%",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            borderBottom: "1px solid #00000020",
            backgroundColor: "#fbfbfb",
            position: "sticky",
            top: 0,
            zIndex: 10,
          }}
        >
          <div>ERP | Groupe SIMON</div>
          <div>
            <Space size={4}>
              <Button
                hidden={!user.role === "admin"}
                onClick={
                  window.location.pathname.match("admin")
                    ? () => navigate("")
                    : () => navigate("admin")
                }
                type={
                  window.location.pathname.match("admin") ? "default" : "text"
                }
                size="small"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                icon={
                  window.location.pathname.match("admin") ? (
                    <RiLogoutBoxLine />
                  ) : (
                    <RiLoginBoxLine />
                  )
                }
              >
                Admin
              </Button>
              <Button
                type="text"
                size="small"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                icon={<RiQuestionFill />}
              />
              <Popover
                arrow={false}
                placement="bottomRight"
                overlayInnerStyle={{
                  padding: 0,
                }}
                trigger="click"
                content={
                  <>
                    <div style={{ width: 440 }}>
                      <div
                        style={{
                          padding: "8px 12px",
                          backgroundColor: "#00000005",
                          borderBottom: "1px solid #00000020",
                          fontWeight: 600,
                        }}
                      >{`${notifications.length} Notifications`}</div>
                      <div style={{ padding: "6px 6px" }}>
                        <List />
                      </div>
                    </div>
                  </>
                }
              >
                <Badge count={notifications.length} size="small">
                  <Button
                    type="text"
                    size="small"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    icon={<RiNotification3Fill />}
                  />
                </Badge>
              </Popover>
              <Popover
                arrow={false}
                placement="bottomRight"
                overlayInnerStyle={{
                  padding: 0,
                }}
                trigger="click"
                content={
                  <>
                    <div style={{ width: 220 }}>
                      <div
                        style={{
                          padding: "8px 12px",
                          backgroundColor: "#00000005",
                          borderBottom: "1px solid #00000020",
                          fontWeight: 600,
                        }}
                      >{`${user.lastName} ${user.firstName}`}</div>
                      <div style={{ padding: "6px 6px" }}>
                        <div className="btnPopOverUser">Paramétres</div>
                        <div className="btnPopOverUser">Mon profil</div>
                        <div
                          className="btnPopOverUser"
                          onClick={() => functions.user.signOut()}
                        >
                          Déconnexion
                        </div>
                      </div>
                      <div
                        style={{
                          padding: "8px 12px",
                          borderTop: "1px solid #00000020",
                          backgroundColor: "#00000005",
                          fontSize: 12,
                          color: "#00000060",
                        }}
                      >
                        APP VERSION V2.0
                      </div>
                    </div>
                  </>
                }
              >
                <Button
                  type="text"
                  size="small"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<RiUser3Fill />}
                />
              </Popover>
            </Space>
          </div>
        </div>
        <div
          style={{
            flex: 1,
            display: "flex",
            flexFlow: "column",
          }}
        >
          <Outlet context={{ user }} />
        </div>
      </div>
    </>
  );
}

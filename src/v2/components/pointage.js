import { Button, DatePicker, Form, Input, Select, TimePicker, message } from "antd";
import { addDoc, collection, getDocs } from "firebase/firestore";
import { auth, db } from "../../services/firebase";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

const ACTIVITY_OPTIONS = [
  { value: "Chantier", label: "Chantier" },
  { value: "Déplacement", label: "Déplacement" },
  { value: "Formation", label: "Formation" },
  { value: "Sous activité", label: "Sous activité" },
  { value: "Absence", label: "Absence" },
  { value: "SAV", label: "SAV" },
  { value: "Divers", label: "Divers" },


];

export default function Pointage() {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      const snapCustomers = await getDocs(collection(db, "customers"));
      const customersData = snapCustomers.docs.map(customer => ({
        id: customer.id,
        ...customer.data(),
      }));
      setCustomers(customersData);
    } catch (error) {
      message.error("Erreur lors du chargement des clients");
      console.error("Erreur fetchCustomers:", error);
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const { date, hours, customer, description, activity } = values;
      const [start, end] = hours;

      await addDoc(collection(db, "timeclock"), {
        date: date.toDate(),
        start: start.toDate(),
        end: end.toDate(),
        state: "En attente",
        created: dayjs().toDate(),
        updated: dayjs().toDate(),
        creator: auth.currentUser.uid,
        creatorEmail: auth.currentUser.email,
        customer: JSON.parse(customer),
        description,
        activity,
        historic: [{
          title: "Création",
          description: `Création du pointage par ${auth.currentUser.email}`,
          created: dayjs().toDate(),
        }],
      });

      message.success("Pointage enregistré avec succès");
      form.resetFields();
    } catch (error) {
      message.error("Erreur lors de l'enregistrement du pointage");
      console.error("Erreur handleSubmit:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        date: dayjs(),
        hours: [dayjs().hour(8).minute(0), dayjs().hour(17).minute(0)]
      }}
    >
      <Form.Item
        label="Date"
        name="date"
        rules={[{ required: true, message: "La date est requise" }]}
      >
        <DatePicker
          style={{ width: "100%" }}
          size="large"
          variant="filled"
          format="dddd DD MMMM YYYY"
        />
      </Form.Item>

      <Form.Item
        label="Horaires"
        name="hours"
        rules={[{ required: true, message: "Les horaires sont requis" }]}
      >
        <TimePicker.RangePicker
          size="large"
          variant="filled"
          style={{ width: "100%" }}
          format="HH:mm"
        />
      </Form.Item>

      <Form.Item
        label="Activité"
        name="activity"
        rules={[{ required: true, message: "L'activité est requise" }]}
      >
        <Select
          size="large"
          variant="filled"
          style={{ width: "100%" }}
          options={ACTIVITY_OPTIONS}
        />
      </Form.Item>
      <Form.Item
        label="Client"
        name="customer"
        rules={[{ required: true, message: "Le client est requis" }]}
      >
        <Select
          size="large"
          placeholder="Recherche par nom ou code postale"
          variant="filled"
          showSearch
          options={customers.map(c => ({
            label: c.name,
            value: JSON.stringify(c),
          }))}
        />
      </Form.Item>

      <Form.Item
        label="Description"
        name="description"
        rules={[{ required: true, message: "La description est requise" }]}
      >
        <Input.TextArea variant="filled" />
      </Form.Item>

      <div style={{ marginTop: 12 }}>
        <Button
          block
          type="primary"
          htmlType="submit"
          loading={loading}
        >
          Valider
        </Button>
      </div>
    </Form>
  );
}

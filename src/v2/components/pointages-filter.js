import { collection, getDocs, query, where } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";
import dayjs from "dayjs";
import { Calendar, Card, Divider, Tag } from "antd";

export default function PointagesFilter({ customer }) {
    const [pointages, setPointages] = useState([]);

    useEffect(() => {
        getDocs(query(collection(db, "timeclock"), where("customer.id", "==", customer.id))).then((snapPointages) => {
            setPointages(snapPointages.docs.map((p) => ({ id: p.id, data: p.data() })));
        });
    }, []);

    return (
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>

            {pointages.length} Pointages
            {pointages.sort((a, b) => dayjs(b.data.date.seconds * 1000).toDate() - dayjs(a.data.date.seconds * 1000).toDate()).map((p) => (

                <div key={p.id} style={{ padding: 12, border: "1px solid #00000020", }}>
                    <div>
                        {`${p.data.user?.firstName} ${p.data.user?.lastName}` || p.data.creatorEmail || "Utilisateur inconnu"}
                    </div>
                    <div><Tag color={p.data.state === "En attente" ? "blue" : p.data.state === "Validé" ? "green" : "red"}>{p.data.state}</Tag></div>
                    <div>{p.data.customer.id}</div>
                    <Divider />
                    <div key={p.id}>{dayjs(p.data.date.seconds * 1000).format("dddd DD MMMM YYYY")}</div>
                    <div>{`${dayjs(p.data.start.seconds * 1000).format("HH:mm")} → ${dayjs(p.data.end.seconds * 1000).format("HH:mm")}`}</div>
                    <div>{p.data.comment || p.data.note || "Aucun commentaire"}</div>
                </div>
            ))}
        </div>
    )
}

import {
  <PERSON><PERSON>,
  Button,
  Drawer,
  Table,
  Tabs,
  message,
  Input,
  Typography
} from "antd";
import {
  collection,
  onSnapshot,
  query,
  orderBy
} from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";
import { SearchOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import AdminCustomerFiles from "../components/admin-customer-files";
import PointagesFilter from "../components/pointages-filter";

const { Title } = Typography;

/**
 * Composant AdminCustomersV2
 * Gestion des clients dans l'interface administrateur
 * Permet de visualiser, filtrer et gérer les clients
 */
export default function AdminCustomersV2() {
  // États locaux
  const [customers, setCustomers] = useState([]); // Liste complète des clients
  const [selectedCustomer, setSelectedCustomer] = useState(null); // Client sélectionné pour affichage détaillé
  const [isLoading, setIsLoading] = useState(true); // État de chargement
  const [zipCodeFilter, setZipCodeFilter] = useState(''); // Filtre par code postal

  // Effet pour charger les clients depuis Firestore
  useEffect(() => {
    setIsLoading(true);

    // Création de la requête avec tri
    const clientsQuery = query(
      collection(db, "customers"),
      orderBy("name", "asc")
    );

    // Abonnement aux modifications de la collection clients
    const unsubscribe = onSnapshot(
      clientsQuery,
      (snapshot) => {
        try {
          const customersData = snapshot.docs.map((doc) => ({
            id: doc.id,
            data: doc.data(),
          }));
          setCustomers(customersData);
          setIsLoading(false);
        } catch (error) {
          console.error("Erreur lors de la récupération des clients:", error);
          message.error("Erreur lors du chargement des clients");
          setIsLoading(false);
        }
      },
      (error) => {
        console.error("Erreur de surveillance Firestore:", error);
        message.error("Erreur de connexion à la base de données");
        setIsLoading(false);
      }
    );

    // Nettoyage de l'abonnement
    return () => unsubscribe();
  }, []);

  // Filtrage des clients selon le code postal
  const filteredCustomers = customers.filter(customer =>
    customer.data.zipCode?.toString().toLowerCase().includes(zipCodeFilter.toLowerCase())
  );

  // Configuration des colonnes du tableau
  const columns = [
    {
      title: "Nom du client",
      key: "name",
      render: (record) => (
        <Button
          size="small"
          type="link"
          onClick={() => setSelectedCustomer(record)}
        >
          {record.data.name}
        </Button>
      ),
      sorter: (a, b) => a.data.name.localeCompare(b.data.name),
    },
    {
      title: "Code postal",
      key: "zipCode",
      dataIndex: ["data", "zipCode"],
      sorter: (a, b) => (a.data.zipCode || '').localeCompare(b.data.zipCode || ''),
      filterDropdown: () => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Filtrer par code postal"
            value={zipCodeFilter}
            onChange={e => setZipCodeFilter(e.target.value)}
            style={{ width: 200, marginBottom: 8, display: 'block' }}
            allowClear // Permet d'effacer facilement le champ
          />
        </div>
      ),
      filteredValue: zipCodeFilter ? [zipCodeFilter] : null,
      filterIcon: filtered => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
    },
    {
      title: "Ville",
      key: "city",
      dataIndex: ["data", "city"],
      sorter: (a, b) => (a.data.city || '').localeCompare(b.data.city || ''),
    },
    {
      title: "Téléphone",
      key: "phone",
      dataIndex: ["data", "phone"],
    },
    {
      title: "Email",
      key: "email",
      dataIndex: ["data", "email"],
    }
  ];

  // Composant pour le titre du drawer avec avatar
  const DrawerTitle = () => (
    selectedCustomer && (
      <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
        <Avatar
          shape="square"
          size="large"
          src={`https://api.dicebear.com/7.x/initials/svg?seed=${selectedCustomer.data.name}&backgroundColor=ff6929`}
        />
        <span style={{ fontSize: '16px', fontWeight: 600 }}>
          {selectedCustomer.data.name}
        </span>
      </div>
    )
  );

  // Gestionnaire pour l'ajout d'un nouveau client
  const handleAddCustomer = () => {
    // TODO: Implémenter la logique d'ajout
    message.info("Fonctionnalité d'ajout à implémenter");
  };

  // Gestionnaire pour l'export des clients
  const handleExport = () => {
    // TODO: Implémenter la logique d'export
    message.info("Fonctionnalité d'export à implémenter");
  };

  return (
    <>
      {/* Drawer pour les détails du client */}
      <Drawer
        styles={{
          header: { backgroundColor: "#00000005" },
          body: { padding: "12px 4%" },
        }}
        destroyOnClose
        extra={
          <Button.Group size="small">
            <Button>Modifier</Button>
            <Button>Imprimer</Button>
          </Button.Group>
        }
        title={<DrawerTitle />}
        maskClosable={false}
        open={Boolean(selectedCustomer)}
        onClose={() => setSelectedCustomer(null)}
        width="50%"
      >
        <Tabs>
          <Tabs.TabPane key="1" tab="Informations" children={<div>
            <div>
              <Title level={5}>Informations</Title>
            </div>
          </div>} />
          <Tabs.TabPane key="2" tab="Affaires" children={<div>Affaires</div>} />
          <Tabs.TabPane key="3" tab="Achats" />
          <Tabs.TabPane key="4" tab="Planning" />
          <Tabs.TabPane key="5" tab="Pointages" >
            <div>Pointages</div>
            <PointagesFilter customer={selectedCustomer} />
          </Tabs.TabPane>
          <Tabs.TabPane key="6" tab="Suivi" />
          <Tabs.TabPane key="7" tab="Documents">
            <AdminCustomerFiles customer={selectedCustomer} />
          </Tabs.TabPane>
        </Tabs>
      </Drawer>

      {/* En-tête de la page */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px 2%",
        }}
      >
        <Title level={4} style={{ margin: 0 }}>Gestion des Clients</Title>
        <Button.Group>
          <Button onClick={handleExport} icon={<DownloadOutlined />}>
            Exporter
          </Button>
          <Button
            type="primary"
            onClick={handleAddCustomer}
            icon={<PlusOutlined />}
          >
            Ajouter
          </Button>
        </Button.Group>
      </div>

      {/* Tableau des clients */}
      <div style={{ padding: "0px 2% 12px" }}>
        <Table
          bordered
          size="small"
          dataSource={filteredCustomers}
          columns={columns}
          loading={isLoading}
          rowKey={(record) => record.id}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total: ${total} clients`,
            showQuickJumper: true, // Permet de sauter rapidement à une page
          }}
          scroll={{ x: 'max-content' }} // Permet le défilement horizontal si nécessaire
        />
      </div>
    </>
  );
}

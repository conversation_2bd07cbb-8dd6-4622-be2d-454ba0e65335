import { But<PERSON>, DatePicker, Drawer, List, Space, Tag } from "antd";
import dayjs from "dayjs";
import {
  collection,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";

export default function AdminHome() {
  const [dates, setDates] = useState([dayjs().startOf("month"), dayjs()]);
  const [openLogs, setOpenLogs] = useState(false);
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    onSnapshot(
      query(
        collection(db, "logs"),
        where("created", ">=", dayjs(dates[0]).toDate()),
        where("created", "<=", dayjs(dates[1]).toDate())
      ),
      (snapOnLogs) =>
        setLogs(snapOnLogs.docs.map((l) => ({ id: l.id, data: l.data() })))
    );
  }, [dates]);

  return (
    <>
      <Drawer
        styles={{
          header: {
            backgroundColor: "#00000005",
            height: 50,
          },
          footer: {
            backgroundColor: "#00000005",
            height: 60,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          },
          body: {
            padding: 0,
            backgroundColor: "black",
          },
        }}
        footer={
          <>
            <div></div>
            <Button.Group>
              <Button
                onClick={() =>
                  logs.map((l) => deleteDoc(doc(db, "logs", l.id)))
                }
              >
                Purger
              </Button>
              <Button>Exporter</Button>
            </Button.Group>
          </>
        }
        height={"70%"}
        onClose={() => setOpenLogs(false)}
        open={openLogs}
        placement="bottom"
        title="Logs"
        extra={
          <>
            <Space>
              <DatePicker.RangePicker
                showWeek
                allowClear={false}
                value={dates}
                onChange={(e) => setDates(e)}
                format={"dddd DD MMMM YYYY"}
              />
            </Space>
          </>
        }
      >
        <List
          dataSource={logs}
          rowKey={(e) => e.id}
          renderItem={(e) => (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: 24,
                fontFamily:
                  "ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace",
                padding: 12,
                color: "white",
              }}
            >
              <div style={{ flex: 0.05 }}>
                <Tag>{e.data.type}</Tag>
              </div>
              <div style={{ flex: 1 }}>{e.data.description}</div>
              <div>{e.data.userEmail}</div>
              <div>
                {dayjs(e.data.created.seconds * 1000).format(
                  "DD/MM/YYYY HH:mm:ss"
                )}
              </div>
            </div>
          )}
        />
      </Drawer>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "24px 2%",
        }}
      >
        <div
          style={{
            fontWeight: 600,
            fontSize: 22,
          }}
        >
          Vue d'ensemble
        </div>
        <div>
          <Space>
            <DatePicker.RangePicker
              showWeek
              allowClear={false}
              value={dates}
              onChange={(e) => setDates(e)}
              format={"dddd DD MMMM YYYY"}
            />
            <Button onClick={() => setOpenLogs(true)}>Logs</Button>
          </Space>
        </div>
      </div>
      <div style={{ flex: 1, padding: "0px 2%" }}>EN COURS</div>
    </>
  );
}

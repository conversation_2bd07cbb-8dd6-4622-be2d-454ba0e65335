import { Button, Form, Input, InputNumber, Modal, Table } from "antd";
import { collection, onSnapshot } from "firebase/firestore";
import { useEffect, useRef, useState } from "react";
import { db } from "../../services/firebase";

export default function AdminSales() {
  const [newSale, setNewSale] = useState(false);
  const [sales, setSales] = useState([]);
  const formRef = useRef();

  useEffect(() => {
    onSnapshot(collection(db, "sales"), (snapOnSales) =>
      setSales(snapOnSales.docs.map((s) => ({ id: s.id, data: s.data() })))
    );
  }, []);

  return (
    <>
      <Modal
        footer={null}
        destroyOnClose
        title="Nouvelle affaire"
        open={newSale}
        onCancel={() => setNewSale(false)}
      >
        <Form
          layout="vertical"
          ref={formRef}
          onChange={(e) => console.log(formRef.current.getFieldValue("cost"))}
        >
          <Form.Item label="Titre" name="title">
            <Input variant="filled" size="large" />
          </Form.Item>
          <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
            <Form.Item style={{ flex: 1 }} label="Prix de revient" name="cost">
              <InputNumber
                decimalSeparator=","
                style={{ width: "100%" }}
                variant="filled"
                size="large"
                suffix="€"
              />
            </Form.Item>
            <Form.Item style={{ flex: 1 }} label="Prix de vente" name="sell">
              <InputNumber
                decimalSeparator=","
                style={{ width: "100%" }}
                variant="filled"
                size="large"
                suffix="€"
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "24px 2%",
        }}
      >
        <div style={{ fontWeight: 600, fontSize: 20 }}>Affaires</div>
        <div>
          <Button.Group>
            <Button>Exporter</Button>
            <Button onClick={() => setNewSale(true)}>Ajouter</Button>
          </Button.Group>
        </div>
      </div>
      <div style={{ padding: "12px 2%" }}>
        <Table
          bordered
          size="small"
          dataSource={sales}
          columns={[
            { title: "Titre", render: (e) => e.data.name },
            { title: "Code devis", render: (e) => e.data.code },
            { title: "Prix de revient", render: (e) => e.data.cost },
            { title: "Prix de vente", render: (e) => e.data.sell },
            { title: "Client", render: (e) => e.data.customer },
            { title: "Société", render: (e) => e.data.costCenter },
            {
              title: "Marge",
              render: (e) =>
                ((e.data.sell - e.data.cost) / e.data.sell).toPrecision(3) *
                100 +
                "%",
            },
          ]}
        />
      </div>
    </>
  );
}

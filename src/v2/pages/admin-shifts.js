import { Button, DatePicker, Input, Select, Table, message } from "antd";
import dayjs from "dayjs";
import { useEffect, useState, useMemo } from "react";
import {
  collection,
  doc,
  onSnapshot,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { db } from "../../services/firebase";
import { filter } from "mathjs";

const ACTIVITY_OPTIONS = [
  { value: "Chantier", label: "Chantier" },
  { value: "Déplacement", label: "Déplacement" },
  { value: "Formation", label: "Formation" },
  { value: "Sous activité", label: "Sous activité" },
  { value: "Absence", label: "Absence" },
];

const STATUS_OPTIONS = [
  { value: "En attente", label: "En attente" },
  { value: "Validé", label: "Validé" },
  { value: "Refusé", label: "Refusé" },
];

export default function AdminShifts() {
  const [dates, setDates] = useState([dayjs().startOf("month"), dayjs()]);
  const [shifts, setShifts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Écouter les changements dans la collection customers
    const unsubscribeCustomers = onSnapshot(
      collection(db, "customers"),
      (snapshot) => {
        const customersData = snapshot.docs.map((doc) => ({
          id: doc.id,
          data: doc.data(),
        }));
        setCustomers(customersData);
      },
      (error) => {
        console.error("Erreur lors du chargement des clients:", error);
        message.error("Erreur lors du chargement des clients");
      }
    );

    return () => unsubscribeCustomers();
  }, []);

  useEffect(() => {
    setLoading(true);
    // Écouter les changements dans la collection timeclock
    const unsubscribeShifts = onSnapshot(
      query(
        collection(db, "timeclock"),
        where("date", ">=", dates[0].toDate()),
        where("date", "<=", dates[1].toDate())
      ),
      (snapshot) => {
        const shiftsData = snapshot.docs.map((doc) => ({
          id: doc.id,
          data: doc.data(),
        }));
        setShifts(shiftsData);
        setLoading(false);
      },
      (error) => {
        console.error("Erreur lors du chargement des pointages:", error);
        message.error("Erreur lors du chargement des pointages");
        setLoading(false);
      }
    );

    return () => unsubscribeShifts();
  }, [dates]);

  const handleStatusChange = async (value, shiftId) => {
    try {
      await updateDoc(doc(db, "shifts", shiftId), {
        state: value,
        updated: dayjs().toDate(),
      });
      message.success("Statut mis à jour avec succès");
    } catch (error) {
      console.error("Erreur lors de la mise à jour du statut:", error);
      message.error("Erreur lors de la mise à jour du statut");
    }
  };

  const calculateDuration = (start, end) => {
    const duration = dayjs(end.seconds * 1000).diff(
      dayjs(start.seconds * 1000),
      "minutes"
    ) / 60;
    return duration.toFixed(2);
  };

  const filteredShifts = useMemo(() => {
    return shifts.filter((shift) =>
      `${shift.data.creatorEmail}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
    );
  }, [shifts, searchQuery]);

  const columns = [
    {
      title: "Collaborateur",
      render: (record) =>
        `${record.data.creatorEmail || record.data.user.email}`,
      width: 200,
      sorter: (a, b) =>
        a.data.creatorEmail.localeCompare(b.data.creatorEmail),
      filterDropdown: () => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Filtrer par collaborateur"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            style={{ width: 200, marginBottom: 8, display: 'block' }}
            allowClear // Permet d'effacer facilement le champ
          />
        </div>
      ),
    },
    {
      title: "Activité",
      width: 160,
      render: (record) => (
        <Select
          value={record.data.activity}
          variant="filled"
          style={{ width: "100%" }}
          placeholder="Vide"
          options={ACTIVITY_OPTIONS}
          onChange={(value) =>
            updateDoc(doc(db, "timeclock", record.id), {
              activity: value,
              updated: dayjs().toDate(),
            })
          }
        />
      ),
    },
    {
      title: "Date",
      render: (record) =>
        dayjs(record.data.date.seconds * 1000).format("dddd DD MMMM YYYY"),
      width: 240,
    },
    {
      title: "Horaires",
      render: (record) =>
        `${dayjs(record.data.start.seconds * 1000).format("HH:mm")} → ${dayjs(
          record.data.end.seconds * 1000
        ).format("HH:mm")}`,
      width: 160,
    },
    {
      title: "Durée",
      render: (record) => {
        const duration = calculateDuration(record.data.start, record.data.end);
        return (
          <div style={{ color: duration > 8.75 ? "red" : "black" }}>
            {duration}
          </div>
        );
      },
    },
    {
      title: "Client",
      render: (record) => (
        <Select
          onChange={(value) =>
            updateDoc(doc(db, "timeclock", record.id), {
              customer: {
                id: value,
              },
              updated: dayjs().toDate(),
            })
          }
          style={{ width: "100%" }}
          variant="filled"
          value={record.data.customer.id}
          options={customers.map((c) => ({
            value: c.id,
            label: c.data.name,
          }))}
        />
      ),
      width: 200,
      filters: customers.map((c) => ({
        value: c.id,
        text: c.data.name,
      })),
      onFilter: (value, record) => record.data.customer.id === value,
      sorter: (a, b) => a.data.customer.id.localeCompare(b.data.customer.id),
    },
    {
      title: "État",
      align: "right",
      render: (record) => (
        <Select
          onChange={(value) => updateDoc(doc(db, "timeclock", record.id), {
            state: value,
            updated: dayjs().toDate(),
          })}
          variant="filled"
          value={record.data.state}
          options={STATUS_OPTIONS}
        />
      ),
    },
  ];

  return (
    <>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "24px 2%",
        }}
      >
        <div>Pointages</div>
        <div style={{ padding: "0px 2%" }}>
          <Input
            variant="filled"
            placeholder="Rechercher un collaborateur"
            onChange={(e) => setSearchQuery(e.target.value)}
            allowClear
          />
          <Button>Expo</Button>
        </div>
        <div>
          <DatePicker.RangePicker
            onChange={setDates}
            value={dates}
            allowClear={false}
            format="dddd DD MMMM YYYY"
          />
        </div>
      </div>
      <div style={{ padding: "12px 2%" }}>
        <Table
          bordered
          showHeader
          size="small"
          loading={loading}
          dataSource={filteredShifts}
          columns={columns}
          rowKey="id"
          pagination={{
            defaultPageSize: 50,
            showSizeChanger: true,
            showTotal: (total) => `Total: ${total} pointages`,
          }}
        />
      </div>
    </>
  );
}

import { collection, doc, onSnapshot, setDoc } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";
import { Button, Col, Form, Input, Modal, Row, Select, Table, message } from "antd";
import dayjs from "dayjs";

const FIREBASE_API_KEY = "AIzaSyCwIBRBKcixwUYNTblk_7Md_b027EaqPnA";
const FIREBASE_SIGNUP_URL = `https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${FIREBASE_API_KEY}`;

const ROLE_OPTIONS = [
    { value: "admin", label: "Admin" },
    { value: "standard", label: "Standard" }
];

const TABLE_COLUMNS = [
    { title: "Prénom", dataIndex: ["data", "firstName"] },
    { title: "Nom de famille", dataIndex: ["data", "lastName"] },
    { title: "Email", dataIndex: ["data", "email"] },
    { title: "Role", dataIndex: ["data", "role"] }
];

export default function AdminUsers() {
    const [users, setUsers] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [form] = Form.useForm();

    useEffect(() => {
        const unsubscribe = onSnapshot(
            collection(db, "users"),
            (snapshot) => {
                const usersData = snapshot.docs.map((doc) => ({
                    id: doc.id,
                    data: doc.data()
                }));
                setUsers(usersData);
            },
            (error) => {
                console.error("Erreur lors de la récupération des utilisateurs:", error);
                message.error("Erreur lors du chargement des utilisateurs");
            }
        );

        return () => unsubscribe();
    }, []);

    const handleSubmit = async (values) => {
        setIsLoading(true);
        try {
            const response = await fetch(FIREBASE_SIGNUP_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    email: values.email,
                    password: values.password,
                    returnSecureToken: true
                })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error?.message || "Erreur lors de la création du compte");
            }

            await setDoc(doc(db, "users", result.localId), {
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                job: values.job,
                role: values.role,
                created: dayjs().toDate(),
                updated: dayjs().toDate()
            });

            message.success("Utilisateur créé avec succès");
            form.resetFields();
            setIsModalOpen(false);
        } catch (error) {
            console.error("Erreur lors de la création de l'utilisateur:", error);
            message.error(error.message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <Modal
                title="Nouveau collaborateur"
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                destroyOnClose
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                >
                    <Row gutter={12}>
                        <Col span={12}>
                            <Form.Item
                                name="firstName"
                                rules={[{ required: true, message: "Veuillez saisir le prénom" }]}
                            >
                                <Input placeholder="Prénom" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="lastName"
                                rules={[{ required: true, message: "Veuillez saisir le nom" }]}
                            >
                                <Input placeholder="Nom de famille" />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item
                        name="email"
                        rules={[
                            { required: true, message: "Veuillez saisir l'email" },
                            { type: "email", message: "Email invalide" }
                        ]}
                    >
                        <Input placeholder="Email" type="email" />
                    </Form.Item>
                    <Form.Item
                        name="password"
                        rules={[
                            { required: true, message: "Veuillez saisir le mot de passe" },
                            { min: 6, message: "Le mot de passe doit contenir au moins 6 caractères" }
                        ]}
                    >
                        <Input.Password placeholder="Mot de passe" />
                    </Form.Item>
                    <Form.Item
                        name="job"
                        rules={[{ required: true, message: "Veuillez saisir le poste" }]}
                    >
                        <Input placeholder="Poste" />
                    </Form.Item>
                    <Form.Item
                        name="role"
                        label="Rôle"
                        rules={[{ required: true, message: "Veuillez sélectionner un rôle" }]}
                    >
                        <Select options={ROLE_OPTIONS} />
                    </Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        block
                        loading={isLoading}
                    >
                        Valider
                    </Button>
                </Form>
            </Modal>

            <div style={{ display: "flex", flexFlow: "column", flex: 1 }}>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: "24px 2%" }}>
                    <h2>Collaborateurs</h2>
                    <Button type="primary" onClick={() => setIsModalOpen(true)}>
                        Ajouter
                    </Button>
                </div>
                <div style={{ padding: "12px 2%" }}>
                    <Table
                        size="small"
                        loading={!users.length}
                        rowKey="id"
                        bordered
                        dataSource={users}
                        columns={TABLE_COLUMNS}
                    />
                </div>
            </div>
        </>
    );
}

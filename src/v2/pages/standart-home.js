import {
  Button,
  DatePicker,
  Space,
  Statistic,
  Table,
  Modal,
  Grid,
} from "antd";
import dayjs from "dayjs";
import { useOutletContext } from "react-router-dom";
import { useEffect, useState } from "react";
import { collection, onSnapshot, query, where } from "firebase/firestore";
import { auth, db } from "../../services/firebase";
import Pointage from "../components/pointage";

const { useBreakpoint } = Grid;

const Header = ({ user, dates, setDates }) => {
  const screens = useBreakpoint();

  return (
    <div style={{
      padding: screens.xs ? "12px 4%" : "24px 2%",
      display: "flex",
      flexDirection: screens.xs ? "column" : "row",
      gap: screens.xs ? "12px" : "0",
      justifyContent: "space-between",
      alignItems: screens.xs ? "stretch" : "center"
    }}>
      <div style={{
        fontSize: screens.xs ? 18 : 22,
        fontWeight: 600
      }}>
        {`${dayjs().hour() < 18 ? "Bonjour" : "Bonsoir"} ${user.firstName}`}
      </div>
      <DatePicker
        onChange={setDates}
        value={dates}
        size={screens.xs ? "middle" : "large"}
        picker="month"
        format="MMMM-YYYY"
        disabledDate={(e) => e > dayjs()}
        style={{ width: screens.xs ? "100%" : "auto" }}
      />
    </div>
  );
};

const Statistics = ({ timeclocks, dates }) => {
  const screens = useBreakpoint();

  const getFilteredCount = (state) => {
    return timeclocks.filter(
      (f) =>
        isWithinMonth(f.data.date.seconds * 1000, dates) &&
        (state ? f.data.state === state : true)
    ).length;
  };

  return (
    <Space
      size={screens.xs ? 12 : 24}
      wrap
      align="center"
      style={{
        width: "100%",
        justifyContent: screens.xs ? "space-between" : "flex-start"
      }}
    >
      <Statistic className="statHome" title="POINTAGES" value={getFilteredCount()} />
      <Statistic className="statHome" title="EN ATTENTE" value={getFilteredCount("En attente")} />
      <Statistic className="statHome" title="VALIDE" value={getFilteredCount("Validé")} />
      <Statistic className="statHome" title="REFUSE" value={getFilteredCount("Refusé")} />
    </Space>
  );
};

const isWithinMonth = (date, monthDate) => {
  const targetDate = dayjs(date);
  return targetDate.isSame(dayjs(monthDate), 'month');
};

export default function StandartHome() {
  const screens = useBreakpoint();
  const { user } = useOutletContext();
  const [dates, setDates] = useState(dayjs().startOf("month"));
  const [timeclocks, setTimeClock] = useState([]);
  const [newShift, setNewShift] = useState(false);

  useEffect(() => {
    const unsubscribeTimeclock = onSnapshot(
      query(
        collection(db, "timeclock"),
        where("creator", "==", auth.currentUser.uid)
      ),
      (snapshot) => setTimeClock(snapshot.docs.map((doc) => ({
        id: doc.id,
        data: doc.data()
      })))
    );

    return () => unsubscribeTimeclock();
  }, []);

  const columns = [
    {
      title: "Date",
      render: (record) => dayjs(record.data.date.seconds * 1000).format("DD/MM/YYYY"),
      sorter: (a, b) => a.data.date.seconds - b.data.date.seconds,
    },
    {
      title: "Horaires",
      render: (record) => (
        `${dayjs(record.data.start.seconds * 1000).format("HH:mm")} → 
         ${dayjs(record.data.end.seconds * 1000).format("HH:mm")}`
      ),
    },
    {
      title: "État",
      dataIndex: ["data", "state"],
    },
    {
      title: "Actions",
      render: (record) => (
        <Button.Group>
          <Button size="small" onClick={() => setNewShift(true)}>
            Modifier
          </Button>
        </Button.Group>
      ),
    },
  ];

  const mobileColumns = [
    {
      title: "Date & État",
      render: (_, record) => {
        const date = dayjs(record.data.date.seconds * 1000);
        const duplicateDates = timeclocks.filter(
          (f) => dayjs(f.data.date.seconds * 1000).format("YYYY/MM/DD") === date.format("YYYY/MM/DD")
        );

        return (
          <div>
            <div style={{
              color: duplicateDates.length > 1 ? "red" : "black",
              fontWeight: "bold"
            }}>
              {date.format("DD/MM/YY")}
            </div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.data.state}
            </div>
          </div>
        );
      },
    },
    {
      title: "Détails",
      render: (_, record) => (
        <div>
          <div>{`${dayjs(record.data.start.seconds * 1000).format("HH:mm")} → 
                 ${dayjs(record.data.end.seconds * 1000).format("HH:mm")}`}</div>
          <div style={{ fontSize: "12px", color: "#666" }}>{record.data.activity}</div>
        </div>
      ),
    },
    {
      render: () => (
        <Button.Group size="small">
          <Button onClick={() => setNewShift(true)}>Modifier</Button>
        </Button.Group>
      ),
      width: 80,
    },
  ];

  return (
    <>
      <Modal
        destroyOnClose
        open={newShift}
        onCancel={() => setNewShift(false)}
        footer={null}
        title="Nouveau pointage"
        width={screens.xs ? "100%" : "520px"}
        style={{
          top: screens.xs ? "auto" : "100px",
          bottom: screens.xs ? 0 : "auto",
          maxWidth: "100vw",
          padding: screens.xs ? 0 : "24px"
        }}
      >
        <Pointage />
      </Modal>

      <Header user={user} dates={dates} setDates={setDates} />

      <div style={{ padding: screens.xs ? "0px 4%" : "0px 2%" }}>
        <div style={{
          padding: "12px 0px 24px",
          display: "flex",
          flexDirection: screens.xs ? "column" : "row",
          gap: screens.xs ? "12px" : "0",
          justifyContent: "space-between"
        }}>
          <div style={{
            fontWeight: 600,
            fontSize: screens.xs ? 16 : 18
          }}>
            Vos pointages sur le mois de {dayjs(dates).format("MMMM YYYY")}
          </div>
          <Button.Group style={{
            display: "flex",
            width: screens.xs ? "100%" : "auto"
          }}>
            <Button style={{ flex: screens.xs ? 1 : "none" }}>Exporter</Button>
            <Button
              type="primary"
              onClick={() => setNewShift(true)}
              style={{ flex: screens.xs ? 1 : "none" }}
            >
              Pointage
            </Button>
          </Button.Group>
        </div>

        <Statistics timeclocks={timeclocks} dates={dates} />

        <div style={{ marginTop: 24 }}>
          <Table
            size="small"
            bordered
            dataSource={timeclocks.filter((f) => isWithinMonth(f.data.date.seconds * 1000, dates))}
            columns={screens.xs ? mobileColumns : columns}
            scroll={{ x: true }}
            pagination={{
              size: screens.xs ? "small" : "default",
              pageSize: screens.xs ? 5 : 10
            }}
          />
        </div>
      </div>
    </>
  );
}
